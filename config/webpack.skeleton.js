const paths = require('./paths');
const { merge } = require('webpack-merge');
const common = require('./webpack.common');
const HtmlWebpackInjectPlugin = require('@didi/html-webpack-inject-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const skeleton = require('../src/components/SkeletonPage/totalSkeleton');

const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP !== 'false';

// gift空间申请完成后修改
const assetsPublicPath = process.env.APP_ENV !== 'prod' ? '/' : '//img-ys011.didistatic.com/static/cooper_cn/cooper/';
const isProd = process.env.NODE_ENV === 'production';

module.exports = [
  merge(common, {
    mode: 'production',
    bail: true,
    devtool: shouldUseSourceMap ? 'source-map' : false,
    entry: {
      blank: paths.blankJs,
    },
    output: {
      path: paths.appBuild,
      // TODO:是否改成chunkHash更合适
      filename: 'static/js/[name].[contenthash:8].js',
      chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
      publicPath: assetsPublicPath,
    },
    plugins: [
      new HtmlWebpackPlugin(
        {
          env_my: process.env.APP_ENV,
          inject: false,
          template: paths.cooperAppHtml,
          chunks: ['main'],
          filename: 'index.html',
          outputPath: paths.appPublic,
          ...(isProd
            ? {
              minify: { // 默认开启html-minifier-terser
                removeComments: true,
                collapseWhitespace: true,
                removeRedundantAttributes: true,
                useShortDoctype: true,
                removeEmptyAttributes: true,
                removeStyleLinkTypeAttributes: true,
                keepClosingSlash: true,
                minifyJS: true,
                minifyCSS: true,
                minifyURLs: true,
              },
            }
            : undefined),
        },
      ),
      new HtmlWebpackInjectPlugin(
        {
          dom: [{ reg: /id="root-skeleton">/, str: skeleton }],
        },
      ),
    ].filter(Boolean),
  }),

];
