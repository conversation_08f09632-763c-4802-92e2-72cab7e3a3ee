.person-card {
  display: flex;
  font-size: 14px;
  color: #17233e;
  align-items: center;
  padding: 9px;


  img {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    margin-right: 12px;
    vertical-align: middle;
  }

  .info {
    flex: 1;
   
    .info-name-msg {
      max-width: 490px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
      line-height: 14px;

      .name {
        display: inline-block;
        max-width: 94px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    
      // 设置群组名最大宽度
      .team-name{
        max-width: 382px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .depart-text{
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 250px;
      }

      .exist-tag {
        background-color:rgba(#8a93a8, .1);
        color: #8a93a8;
        font-size: 12px;
        line-height: 12px;
        border-radius: 4px;
        padding: 1px 4px;
      }
    }

    

    .info-text-msg {
      margin-top: 6px;
      font-size: 12px;
      line-height: 10px;
      color: #8a93a8;
    }

    
  }
}
