.primary-modal {
  input {
    &:focus {
      border: 1px solid @primary-1 !important;
    }
  }

  .ant-modal-content {
    border-radius: 8px !important;
  }

  .ant-modal-body {
    padding: 0 32px;
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0 !important;
    padding: 32px 32px 16px;
    border-bottom: 1px solid transparent;
  }

  .ant-modal-footer {
    border-top: 1px solid transparent;
    padding: 10px 32px 32px;
  }

  .ant-modal-close {
    top: 22px;
    right: 14px;
  }

  .dk-iconfont {
    font-size: 28px;
  }

  .dk-iconfont:hover {
    background-color: @blueGray-16;
    border-radius: 4px;
  }

}

.folderTree-noAuthModal {
  :global {
    .ant-modal-content {
      .ant-modal-confirm-btns {
        > button:first-child {
          display: none;
        }
      }
    }
  }
}

.create-team-space {
  .ant-modal {
    padding: 0;

    .ant-modal-close {
      display: none;
    }

    .ant-modal-body {
      padding: 0;
    }
  }

  .modal-close {
    color: @blueGray-7;
    font-size: 24px;
    position: absolute;
    top: 24px;
    right: 24px;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    line-height: 24px !important;
  }

  .modal-close:hover {
    background-color: @blueGray-16;
  }
}