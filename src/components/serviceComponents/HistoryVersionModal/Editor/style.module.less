.editor {
  position: relative;
  z-index: 0;
  height: 100%;
  overflow: auto;

  .editor-container {
    height: 100%;
    :global {
      .didoc-editor-app {
        .editor-menu-wrapper {
          display: none;
        }
        .editor {
          .editor-title {
            .editorTitle();
            padding: 18px 40px!important;
            margin-top:22px !important;
            .delete {
              background-color: rgba(255, 195, 203, 0.6);
              text-decoration: line-through;
            }
            .delete:hover {
              cursor: pointer;
              background-color: rgba(255, 195, 203, 0.8);
            }
            .add {
              background-color: #D4F3E2;
            }
            .add:hover {
              cursor: pointer;
              background-color: #BDE6D5;
            }
          }

          .didoc-editor-container {
            padding: 0 40px 15px!important;
          }

          .loading {
            display: none;
          }
        }
      }
    }
  }
}

.wiki-transfer-fail-tip {
  padding: 24px 64px;
  font-size: 16px;
  line-height: 1.5;
}

.wiki-transfer-fail-tip__link {
  color: #3F81FF;
  word-break: break-all;
}
