import React, { useEffect, useState, useMemo } from "react";
import { intl } from "di18n-react";
import classNames from "classnames/bind";
import CopyToClipboard from "react-copy-to-clipboard";
import DropdownCheckbox from "@/components/common/DropdownCheckbox";
import DropdownRadio from "@/components/common/DropdownRadio";
import DisabledTip from "@/components/common/DisableTip";
import { formatDatetimeNew } from "@/utils/cooperutils";
import styles from "./style.module.less";
import { modifyShareLink } from "@/service/knowledge/share";
import { modifyShareLinkCooper } from "@/service/cooper/home";
import {
  PERM_DEFAULT_LIST_KNOWLEDGE,
  PERM_DEFAULT_LIST_SHARE,
} from "@/components/CooperFoldAuth/contants";
import { IN_OUT } from "@/constants/space";
import { message } from "antd";
import { sendFileTypeEvent } from "@/utils/type";
import {
  permissionOptions,
  accessingLinkOption,
  permissionOptionsDoc,
  passwordOptions,
} from "./config";

const cx = classNames.bind(styles);

const NewLinkShareSetting = (props) => {
  const {
    isDkPage = true,
    linkInfo = {},
    refreshLinkInfo,
    info,
    disabled,
    adminPerm,
  } = props;
  const {
    accessRole,
    permission,
    expire_time,
    password,
    expiration,
    access_type,
    resource_id,
    invite_link_id,
    spaceType,
  } = linkInfo;

  const [newPassword, setPassword] = useState(password); // 新密码
  const [needPassword, setNeedPassword] = useState(access_type === "Secret");

  useEffect(() => {
    setPassword(password);
  }, [password]);

  useEffect(() => {
    setNeedPassword(access_type === "Secret");
  }, [access_type]);

  const generatePwd = () => {
    return Math.floor(100000 + Math.random() * 900000);
  };

  const DkpermOptions = useMemo(() => {
    return isDkPage ? permissionOptions() : permissionOptionsDoc();
  }, isDkPage);

  const disabledTitle = useMemo(() => {
    return disabled ? intl.t("仅文档管理员可修改设置") : "";
  }, [disabled]);

  const getPerm = (values) => {
    const selectPerms = DkpermOptions.filter((item) =>
      values.includes(item.id)
    );
    const prem = selectPerms.reduce((sum, item) => sum + item.perm, 0);
    return prem;
  };

  // 处理密码变更
  const handleChangePwd = (value, _newPassword) => {
    const isNeedPassword = value === "Secret";
    const omegaKey = isNeedPassword
      ? "ep_share_link_manage_password_ck"
      : "ep_share_link_manage_unpassword_ck";
    sendFileTypeEvent(omegaKey, info?.fileType);
    setPassword(_newPassword);
    setNeedPassword(isNeedPassword);
  };

  // change
  const handleChange = (changekey, value) => {
    const params = {
      resource_id,
      invite_link_id,
      permission: permission,
      password: newPassword,
      expiration: expiration || 3,
      accessRole,
      access_type: needPassword ? "Secret" : "Public",
      linkSelectType: 1,
    };
    params[changekey] = value;
    // 密码特殊处理
    if (changekey === "access_type") {
      const _newPassword = generatePwd();
      params.password = _newPassword;
      handleChangePwd(value, _newPassword);
    }
    const fn = isDkPage ? modifyShareLink : modifyShareLinkCooper;
    fn(params).then(() => {
      message.success(intl.t("修改成功"));
      refreshLinkInfo();
    });
  };

  const resetPwd = () => {
    const _newPassword = generatePwd();
    setPassword(_newPassword);
    handleChange("password", _newPassword);
  };
  const permsValue = useMemo(() => {
    let defaultperm = [];
    const permMap = isDkPage
      ? PERM_DEFAULT_LIST_KNOWLEDGE
      : PERM_DEFAULT_LIST_SHARE;
    for (let i = 0; i < permMap.length; i++) {
      if (permission & permMap[i]) {
        defaultperm.push(i);
      }
    }
    return defaultperm;
  }, [permission, isDkPage]);

  return (
    <div className={cx("share-content")}>
      <div className={cx("share-link-setting")}>
        <span>{intl.t("通过链接访问的人")}：</span>
        <DisabledTip title={disabledTitle}>
          <span className={cx({ "share-link-setting-disabled": disabled })}>
            <DropdownRadio
              options={accessingLinkOption()}
              onChange={(value) => {
                handleChange("accessRole", value);
                const omegaKey = !!value
                  ? "ep_share_link_manage_invite_ck"
                  : "ep_share_link_manage_uninvite_ck";
                sendFileTypeEvent(omegaKey, info?.fileType);
              }}
              value={accessRole}
              isShareDropdownRadio={true}
            />
          </span>
        </DisabledTip>
      </div>
      <div className={cx("share-link-setting")}>
        <span>{intl.t('权限')}：</span>
        <DisabledTip title={disabledTitle}>
          <span className={cx({ "share-link-setting-disabled": disabled })}>
            <DropdownCheckbox
              placement="left"
              showDesc
              options={DkpermOptions}
              value={permsValue}
              onChange={(values) => {
                handleChange("permission", getPerm(values));
                sendFileTypeEvent(
                  "ep_share_link_manage_permission_ck",
                  info?.fileType
                );
              }}
              splitLine={true}
              isSharedropdownCheckbox={isDkPage}
            />
          </span>
        </DisabledTip>
      </div>
      <div className={cx("share-link-setting")}>
        <span className={cx("link-setting-label")}>{intl.t('有效期')}：</span>
        <DisabledTip title={disabledTitle}>
          <span className={cx({ "share-link-setting-disabled": disabled })}>
            <DropdownRadio
              options={[
                {
                  id: 1,
                  label: intl.t("永久有效"),
                  disabled: (info?.relationTypeTags || []).includes(IN_OUT),
                },

                {
                  id: 2,
                  label: `1${intl.t("个月1")}`,
                },

                {
                  id: 3,
                  label: `1${intl.t("周1")}`,
                },

                {
                  id: 4,
                  label: `1${intl.t("天1")}`,
                },
              ]}
              onChange={(value) => {
                handleChange("expiration", value);
                sendFileTypeEvent(
                  "ep_share_link_manage_modifyperiod_ck",
                  info?.fileType
                );
              }}
              value={expiration || 3}
              isShareDropdownRadio={true}
            />
          </span>
        </DisabledTip>
        {expiration !== 1 && (
          <span>
            <span
              className={cx("share-expiration-date", {
                "expiration-date-disable": disabled,
              })}
            >
              {formatDatetimeNew(expire_time)}
            </span>
            <span>{intl.t("过期后，重置为“未开启分享”")}</span>
          </span>
        )}
      </div>
      <div className={cx("share-link-setting")}>
        <span className={cx("link-setting-password")}>{intl.t('密码')}：</span>
        <DisabledTip
          title={
            spaceType === "IN_OUT"
              ? intl.t("外部空间的文档必须设置密码")
              : disabledTitle
          }
        >
          <span
            className={cx({
              "share-link-setting-disabled": disabled || spaceType === "IN_OUT",
            })}
          >
            <DropdownRadio
              options={passwordOptions()}
              onChange={(value) => {
                const _accessType = passwordOptions()[value].access_type;
                handleChange("access_type", _accessType);
              }}
              value={needPassword ? 0 : 1}
              isShareDropdownRadio={true}
            />
          </span>
        </DisabledTip>
        {adminPerm && needPassword && (
          <div className={cx("setting-password")}>
            <div className={cx("setting-password-date")}>
              <span>{newPassword}</span>
            </div>
            <CopyToClipboard
              text={newPassword}
              onCopy={() => {
                message.success(intl.t("已复制密码"));
              }}
            >
              <div className={cx("setting-password-copy")}>
                <i className="dk-iconfont dk-icon-lianjie-01" />
                <span>{intl.t("复制")}</span>
              </div>
            </CopyToClipboard>
            <DisabledTip title={disabledTitle}>
              <span className={cx({ "share-link-setting-disabled": disabled })}>
                <div
                  className={cx("setting-password-reset")}
                  onClick={resetPwd}
                >
                  <i className="dk-iconfont dk-icon-zhongxinshengcheng" />
                  <span>{intl.t("重置")}</span>
                </div>
              </span>
            </DisabledTip>
          </div>
        )}
        {!adminPerm && needPassword && <span>{intl.t("仅文档管理员可见密码")}</span>}
      </div>
    </div>
  );
};

export default NewLinkShareSetting;
