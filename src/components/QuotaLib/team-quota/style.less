.team-quota {
  // display: flex;
  // flex-direction: column;
  // justify-content: flex-start;
  // align-items: center;
  .quota-btn {
    margin-top: 12px;
    border-radius: 4px;
    background: rgba(26, 110, 255, 0.1);
    color: @primary-color-new;
    font-size: 14px;
    border: none;

    >span {
      font-size: 14px;
    }

    &:focus {
      border-color: #c9cfd7;
      color: #2F343C;
    }
    &:hover {
      border-color: #1A75FF;
      color: #1A75FF;
    }
  }
  .quota-detail {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    
  }
  .quota-tip {
    margin-top: 4px;
    font-size: 12px;
    color: @blueGray-6;
    padding: 0 13px;
  }

  .quota-word {
    font-size: 15px;
    font-family: PingFangSC-Medium;
    font-weight: normal;
    color: #222A35;
    margin-left: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 160px;
    margin-top: 12px;
    

    .quota-word-icon {
      margin-left: 5px;
    }
  }
  .red-word {
    color: #FF563B;
  }

  .quota-progress {
    margin-left: 14px;
    width: 280px;

    .ant-progress-bg {
      height: 4px !important;
    }
  }
}

.team-quota-capacity {
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: normal;
  line-height: 24px;
  letter-spacing: 0em;
  color: @blueGray-color;
}

.space-quota-modal {
  border-radius: 8px;
  .team-quota-hint {
    font-size: 14px;
    color: #8a93a8;
    .team-quota-hint-num {
      font-size: 14px;
      color: #0066ff;
    }
  }
  .ant-confirm-btns button {
    padding: 0;
  }
  .icon-local-container {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
  }

  .ant-modal-content {
    border-radius: 8px !important;
  }
}

.quota-detail-tooltip-inner {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .tooltip-inner-front {
    font-size: 12;
    color: #fff;
  }
  .tooltip-inner-end {
    font-size: 12;
    color: #0066ff;
    cursor: pointer;
  }
}

.dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #D8DEE3;; 
  margin-right: 7px;
  position: relative;
  top: -2px;
}