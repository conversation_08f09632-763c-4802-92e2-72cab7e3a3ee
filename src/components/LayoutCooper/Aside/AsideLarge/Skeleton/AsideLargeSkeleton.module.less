.aside-skeleton-box {
  width: 240px;
  padding: 0px 8px;

  .aside-skeleton-button {
    // border: 1px solid black;
    * {
      min-width: auto !important; // 覆盖 antd 下同
      width: 100% !important;
      height: 100% !important;
    }
  }


  .aside-skeleton-box-item {
    width: 100%;
    height: 36px;
    // padding: 7px 24px;
    position: relative;

    .aside-skeleton-logo {
      position: absolute;
      top: 10px;
      left: 24px;
      width: 16px;
      height: 16px;
      // border-radius: 2px;
      // opacity: 1;
      // background: #EAECEE;
    }

    .aside-skeleton-title {
      position: absolute;
      top: 11px;
      left: 52px;
      width: 48px;
      height: 14px;

    }

  }

  .aside-skeleton-space {
    width: 100%;

    .aside-skeleton-space-item {
      width: 100%;
      height: 35px;
      position: relative;

      .aside-skeleton-symbol {
        display: block;
        position: absolute;
        top: 15.5px;
        left: 52px;
        width: 4px;
        height: 4px;
      }

      .aside-skeleton-title {
        display: block;
        position: absolute;
        top: 11px;
        left: 64px;
        width: 88px;
        height: 13px;
      }
    }
  }
}