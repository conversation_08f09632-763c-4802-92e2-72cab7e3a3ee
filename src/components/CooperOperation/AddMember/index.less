// @import '../../theme.scss';

.tab-modal {
  .ant-modal {
    width: 640px !important;
    .ant-modal-content {
      border-radius: 12px;
    }

    .direct-add .foot button:last-child {
      background-color: @primary-color-new;
    }

    .batch-add .msg-and-btn .confirm-btn {
      background-color: @primary-color-new;
    }

    .link-invite .ant-btn-primary {
      background-color: @primary-color-new;
    }

    .ant-modal-close {
      top: 16px;
      right: 14px;
    }
  
    .ant-modal-close-x {
      font-size: 18px;
      font-weight: 100;
      color: #bec5d2;
  
      &:hover {
        color: #0066FF;
      }
    }
  }
  .batch-add-title {
    line-height: 28px;
    display: flex;
    align-items: center;
    .back {
      cursor: pointer;
      // margin-right: 16px;
      // width: 24px;
      // height: 24px;
      font-size: 24px;
      margin-right: 4px;
      // background: url('../../../assets/back.png') no-repeat;
      // background-size: contain;
    }
  }

  .hidden {
    display: none;
  }

  .ant-modal-header {
    border-bottom: 0;
    padding: 32px 32px 16px;
    border-radius: 12px;

    .ant-modal-title {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      height: 25px;
      line-height: 25px;
      color: #000000;
      .add-title {
        display: flex;
        align-items: center;
        .add-title-tip {
          margin-left: 5px;
          display: inline-block;
          width: 18px;
          height: 18px;
          background: url('./icon/addTip.png') no-repeat;
          background-size: contain;
        }
      }


      span {
        margin-right: 12px;
      }
    }
  }

  .ant-modal-body {
    padding: 0;
    position: relative;
    .add-tip {
      position: absolute;
      left: 24px;
      bottom: 25px;
      color: #999999;
      line-height: 20px;
      font-size: 14px;
    }
  }
}

.addMember-modalTitleTip {
  max-width: 410px !important;
  
  .ant-tooltip-arrow {
    display: none;
  }
  .ant-tooltip-inner {
    padding: 20px 24px !important;
    min-height: auto !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    color: #666666 !important;
    background-color: #fff !important;
    line-height: 20px !important;
    border-radius: 6px !important;
    max-width: max-content !important;
    width: 410px !important;
  }
  
  .title {
    color: #333333;
    margin-bottom: 10px;
  }
  .desc {
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.addMember-tabs {
  .ant-tabs-nav-wrap {
    padding: 0 32px;
  }

  .direct-add {
    padding: 0 32px 32px
  }
}
