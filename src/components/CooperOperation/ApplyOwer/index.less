
.apply-owner {
  margin: 22px 0 40px;
  color: #94a0b0 ;;
  
  .title {
    font-size: 14px;

    > span {
      float: right;
    }
  }

  textarea {
    display: block;
    font-size: 14px;
    color: #2f343c;
    width: 100%;
    height: 156px;
    margin: 4px 0;
    padding: 8px;
    border: 1px solid #c9cfd7;
    border-radius: 4px;
    resize: none;
    outline: none;

    &:focus {
      border: 1px solid #0066ff;
      box-shadow: 0 0 0 2px rgba(0,102,255,0.20);
    }
  }

  .warn {
    color: #ff3f5a;
  }

  .foot {
    height: 17px;

    > span {
      margin-right: 4px;
      vertical-align: middle;
    }

    > a {
      margin: 0 4px;
      display: flex;
      align-items: center;
      color: #94a0b0;
    }
    > a:hover {
      color: #0066ff;
    }
    .dk-iconfont {
      font-size: 19px;
    }
  }
}

.team-apply-owner-ap {
  .ant-modal-footer button:last-child {
    background-color: @primary-color-new;
    margin-left: 10px;
    border: 0;
    color: #fff;
  }
}
