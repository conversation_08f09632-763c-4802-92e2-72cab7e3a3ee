import { intl } from 'di18n-react';
import { useContext, useEffect, useMemo, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import classNames from 'classnames/bind';
import usePermission from '@/hooks/usePermission';
import ErrorTips from '@/components/ErrorTipsDk';
import { PageNotPublishIcon, OwnerPageNotPublishIcon } from '@/assets/icon';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import Header from '@/pages/knowledge/PageDetail/Header';
import HeaderSimple from './Header';
import { inPhone } from '@/utils';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import ExpireLink from '@/pages/knowledge/ExpireLink';
import styles from './style.module.less';

const isInPhone = inPhone();

const cls = classNames.bind(styles);

function Authority(props) {
  const {
    children,
    docInfo = {},
    profile = {},
    getPageDetail,
    changeInitLoading,
  } = props;
  const { latestVersion, permission } = docInfo;
  const navigate = useNavigate();
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const { checkOperationPermission } = usePermission();
  const [isDelete, setIsDelete] = useState(null);
  const notification = useNotification();
  const { permission: dkPerm } = useSelector((state) => state.KnowledgeData);
  const { tree } = useSelector((state) => state.PageTree);
  const { teamId } = useParams();
  const isSelf = profile.username === docInfo.createBy;

  const hasAsidePerm = useMemo(() => {
    return checkOperationPermission('READ_DK', dkPerm.perm);
  }, [dkPerm]);

  useEffect(() => {
    if (pageId !== docInfo?.pageId) {
      getPageDetail({ pageId })
        .then((detailsInfo) => {
          const { pageId: newPageId, pageName: newPageName, latestVersion } = detailsInfo;
          if (tree.updateNode) {
            // 页面成员没有目录权限时，没有tree
            tree.updateNode(newPageId, {
              title: newPageName.trim(),
              pageName: newPageName.trim(),
            });
          }

          // 未发布，隐藏loading效果
          if (!latestVersion && document.querySelector('.page-detail-loading')) {
            document.querySelector('.page-detail-loading').style.zIndex = -1;
          }
        })
        .catch((err) => {
          if (err.errorCode === 201003 || err.errorCode === 501003) {
            // 页面被删除
            setIsDelete(true);
          } else if (err.errorCode === 201082) {
            notification(NotificationStatus.ERROR, intl.t('页面无权限'));
          } else if (err.errorCode && err.errorCode !== 401) {
            notification(
              NotificationStatus.ERROR,
              intl.t('服务器错误，请稍后再试'),
            );
          }
        });
    }
  }, [pageId]);

  // TODO: 需要改成有完整目录权限时才跳转
  useEffect(() => {
    // 有知识库的查看权限时被删除，跳转到首页，没有的话不动
    if (hasAsidePerm && isDelete && !inPhone()) {
      notification(
        NotificationStatus.ERROR,
        intl.t('页面已被删除，即将跳转到首页'),
      );
      setTimeout(() => {
        const url = window.location.origin + `/knowledge/${knowledgeId}/home`;
        window.location.href = url;
      }, 1000);
    }
  }, [hasAsidePerm, isDelete]);

  const isReleased = useMemo(() => {
    if (docInfo.latestVersion === undefined) return null;
    return !!latestVersion;
  }, [latestVersion]);

  const goToEditor = () => {
    changeInitLoading(true);
    if (window.top !== window.self) {
      window.open(`/knowledge/${knowledgeId}/${pageId}/edit`)
    } else {
      changeInitLoading(true);
      navigate(
        teamId
          ? `/team-file/${teamId}/knowledge/${knowledgeId}/${pageId}/edit`
          : `/knowledge/${knowledgeId}/${pageId}/edit`,
      );
    }
  };

  const hasEditPermission = useMemo(() => {
    return checkOperationPermission('MANAGE_PAGE_CONTEXT', permission);
  }, [permission]);

  const title = useMemo(() => {
    if (!hasEditPermission) {
      return (
        <div className={cls('no-permission-tip')}>
          {intl.t('此页面暂未发布，暂不可见')}
        </div>
      );
    }
    return (
      <div className={cls('no-permission-tip')}>
        {isSelf ? intl.t('你尚未发布页面') : intl.t('该页面尚未发布')}
        {!isInPhone && <span>，</span>}

        {!isInPhone && (
          <span
            className={cls('link')}
            onClick={goToEditor}>
            {intl.t('继续编辑')}
          </span>
        )}
      </div>
    );
  }, [goToEditor]);

  return (
    <div className={cls('authority')}>
      {!isInPhone && (isReleased ? <Header /> : <HeaderSimple />)}

      {isDelete && <ExpireLink />}

      {!isDelete && (
        <>
          {isReleased && (
            <>
              {children}
            </>
          )}

          {isReleased === false && (
            <ErrorTips
              imgClassName={'publish-icon'}
              img={isSelf ? OwnerPageNotPublishIcon : PageNotPublishIcon}
              title={title}
            />
          )}
        </>
      )}
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo, editReady } = pageDetail;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    editReady,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const { getPageDetail, changeInitLoading } = pageDetail;
  return {
    getPageDetail,
    changeInitLoading,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Authority);
