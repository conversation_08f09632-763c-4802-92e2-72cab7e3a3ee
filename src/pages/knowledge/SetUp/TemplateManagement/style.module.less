.template-management {
  height: calc(100vh - 245px);
  overflow-x: auto;

  .container {
    display: flex;
    height: 100%;
    min-width: 650px;

    .sidebar {
      display: flex;
      flex-direction: column;
      width: 224px;
      height: 100%;
      border-right: 1px solid #E8EAED;
      overflow-x: hidden;
      overflow-y: auto;

      .new {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 188px;
        height: 40px;
        color: #FFFFFF;
        background: @primary-color;
        border-radius: 4px;
        margin: 0 18px 16px;
        border: none;
        font-size: 16px;
        font-weight: 500;

        &:hover, &:focus {
          color: #FFFFFF;
          background: @primary-color;
        }

        &::after {
          display: none;
        }

        &:disabled {
          opacity: 0.5;
        }

        .new-icon {
          font-size: 24px;
        }
      }

      .menu {
        flex: 1;
        overflow: auto;
      }
    }

    .main {
      width: calc(100% - 224px);

      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        font-size: 12px;
        padding-left: 24px;

        + .content {
          height: calc(100% - 48px);
        }

        .title {
          color: rgba(34, 42, 53, 0.4);
        }

        .options {
          display: inline-flex;
          align-items: center;

          .edit, .rename, .delete {
            display: inline-flex;
            align-items: center;
            color: #505050;
            margin-left: 24px;
            cursor: pointer;

            &:hover {
              color: @primary-color;
            }
          }

          .edit-icon, .rename-icon, .delete-icon {
            width: 16px;
            font-size: 16px;
            margin-right: 3px;
          }
        }
      }

      .content {
        height: 100%;
        overflow: auto;

        .thumbnail {
          width: 100%;
          object-fit: contain;
        }
      }
    }
  }
}

.delete-template-modal {
  padding-bottom: 0;

  :global {
    .ant-modal-content {
      border-radius: 6px;

      .ant-modal-body {
        padding: 32px 28px 24px;

        .ant-modal-confirm-content {
          margin-top: 10px;
          font-size: 16px;
        }

        .ant-modal-confirm-title {
          font-size: 24px;
        }

        .ant-modal-confirm-btns {
          margin-top: 36px;

          .ant-btn {
            width: 96px;
            height: 44px;
            background: #F6F6F6;
            border-radius: 4px;
            border: none;
            font-size: 16px;
            color: #444B4F;

            &::after {
              display: none;
            }

            &.ant-btn-primary {
              margin-left: 16px;
              color: #FFFFFF;
              background: @primary-color;
            }
          }
        }
      }
    }
  }

  .tip-content {
    font-size: 16px;
    color: #444B4F;
    line-height: 24px;
    margin-top: 10px;

    .datetime {
      color: #444B4F;
      font-weight: 500;
    }
  }
}
