process.env.NODE_ENV = "development";
process.env.BABEL_ENV = "development";
process.env.GENERATE_SOURCEMAP = "true";
const targetPort = process.env.TARGET_PORT;

const webpack = require("webpack");
const WebpackDevServer = require("webpack-dev-server");
const chalk = require("react-dev-utils/chalk");
const openBrowser = require("react-dev-utils/openBrowser");
const { checkBrowsers } = require("react-dev-utils/browsersHelper");
const {
  createCompiler,
  prepareUrls,
} = require("react-dev-utils/WebpackDevServerUtils");
const paths = require("../config/paths");
const cooperDevWebpackConfig = require("../config/webpack.cooper.dev");
const konwledgeDevWebpackConfig = require("../config/webpack.konwledge.dev");
const devServerConfig = require("../config/webpack.devServer");

const entryServerArr = [
  {
    config: cooperDevWebpackConfig,
    HOST: devServerConfig.host,
    PORT: 4001,
    appName: require(paths.appPackageJson).name,
    devServerConfig: {
      ...devServerConfig,
      ...{
        port: 4001,
        onBeforeSetupMiddleware: (devServer) => {
          devServer.app.use((req, res, next) => {
            if (req.url.startsWith("/knowledge")) {
              const newUrl = "http://localhost:4002" + req.url;
              res.redirect(newUrl);
            } else {
              next();
            }
          });
        },
      },
    },
    path: "/",
  },
  {
    config: konwledgeDevWebpackConfig,
    HOST: devServerConfig.host,
    PORT: 4002,
    appName: require(paths.appPackageJson).name,
    devServerConfig: { ...devServerConfig, ...{ port: 4002, onBeforeSetupMiddleware: (devServer) => {
      devServer.app.use((req, res, next) => {
        if (req.url === "/") {
          const newUrl = "http://localhost:4001" + req.url;
          res.redirect(newUrl);
        } else {
          next();
        }
      });
    }} },
    path: "/knowledge",
  },
];

checkBrowsers(paths.appPath)
  .then(() => {
    const serversToStart = targetPort
      ? entryServerArr.filter((item) => item.PORT === parseInt(targetPort))
      : entryServerArr; // 如果没有传递 TARGET_PORT，则启动所有服务

    serversToStart.forEach((item) => {
      const { config, HOST, PORT, appName, devServerConfig, path } = item;
      const urls = prepareUrls("http", HOST, PORT, path);
      const devSocket = {
        warnings: (warnings) =>
          devServer.sockWrite(devServer.sockets, "warnings", warnings),
        errors: (errors) =>
          devServer.sockWrite(devServer.sockets, "errors", errors),
      };
      const compiler = createCompiler({
        appName,
        config,
        devSocket,
        urls,
        webpack,
      });
      const devServer = new WebpackDevServer(compiler, devServerConfig);
      devServer.listen(PORT, HOST, (err) => {
        if (err) {
          return console.log(err);
        }

        console.log(chalk.cyan("Starting the development server...\n"));
        openBrowser(urls.localUrlForBrowser);
      });
    });
  })
  .catch((err) => {
    if (err && err.message) {
      console.log(err.message);
    }
    process.exit(1);
  });
