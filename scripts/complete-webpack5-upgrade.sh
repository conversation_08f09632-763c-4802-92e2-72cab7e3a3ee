#!/bin/bash

# Webpack 5 完整升级脚本
# 基于官方升级指南和项目实际情况

set -e

echo "🚀 开始Webpack 5升级..."

# 1. 备份当前配置
echo "📋 备份当前配置..."
cp package.json package.json.backup
cp -r config config.backup

# 2. 清理旧依赖
echo "🧹 清理旧依赖..."
rm -rf node_modules package-lock.json

# 3. 移除不兼容的依赖
echo "❌ 移除不兼容的依赖..."
npm uninstall \
  optimize-css-assets-webpack-plugin \
  file-loader \
  url-loader \
  node-sass \
  --legacy-peer-deps

# 4. 安装webpack 5核心
echo "⬆️ 升级webpack核心..."
npm install --save-dev \
  webpack@^5.95.0 \
  webpack-dev-server@^4.15.2 \
  webpack-merge@^5.10.0 \
  --legacy-peer-deps

# 5. 安装替代依赖
echo "🔄 安装替代依赖..."
npm install --save-dev \
  css-minimizer-webpack-plugin@^7.0.0 \
  sass@^1.77.0 \
  --legacy-peer-deps

# 6. 升级兼容的loader和plugin
echo "📦 升级loader和plugin..."
npm install --save-dev \
  @pmmmwh/react-refresh-webpack-plugin@^0.5.15 \
  autoprefixer@^10.4.20 \
  babel-loader@^9.2.1 \
  case-sensitive-paths-webpack-plugin@^2.4.0 \
  copy-webpack-plugin@^12.0.2 \
  css-loader@^6.11.0 \
  eslint-webpack-plugin@^4.0.1 \
  html-webpack-plugin@^5.6.0 \
  less-loader@^12.2.0 \
  mini-css-extract-plugin@^2.9.1 \
  postcss-loader@^8.1.1 \
  postcss-safe-parser@^7.0.1 \
  react-dev-utils@^12.0.1 \
  react-refresh@^0.14.2 \
  resolve-url-loader@^5.0.0 \
  sass-loader@^16.0.2 \
  style-loader@^3.3.4 \
  terser-webpack-plugin@^5.3.10 \
  webpack-bundle-analyzer@^4.10.2 \
  --legacy-peer-deps

# 7. 重新安装所有依赖
echo "🔄 重新安装依赖..."
npm install --legacy-peer-deps

# 8. 测试构建
echo "🧪 测试构建..."
if npm run build:test; then
    echo "✅ 构建成功！"
    
    # 9. 运行验证脚本
    echo "🔍 运行验证..."
    if node scripts/validate-webpack5-upgrade.js; then
        echo "🎉 Webpack 5升级完成！"
        echo ""
        echo "📊 升级总结："
        echo "- Webpack版本: $(npm list webpack --depth=0 | grep webpack)"
        echo "- 构建测试: ✅ 通过"
        echo "- 功能验证: ✅ 通过"
        echo ""
        echo "🔧 下一步建议："
        echo "1. 测试开发服务器: npm start"
        echo "2. 测试所有构建环境: npm run build:qa && npm run build:prod"
        echo "3. 运行完整的功能测试"
        echo "4. 检查构建性能对比"
    else
        echo "⚠️ 验证失败，请检查具体问题"
        exit 1
    fi
else
    echo "❌ 构建失败，开始回滚..."
    
    # 回滚
    echo "🔄 回滚到升级前状态..."
    cp package.json.backup package.json
    rm -rf config
    cp -r config.backup config
    rm -rf node_modules package-lock.json
    npm install --legacy-peer-deps
    
    echo "💡 回滚完成。请检查错误日志并手动解决问题。"
    echo ""
    echo "🔍 常见问题解决方案："
    echo "1. 依赖版本冲突 - 使用 --legacy-peer-deps"
    echo "2. Node.js版本问题 - 确保使用Node.js 12+"
    echo "3. 第三方依赖不兼容 - 查找替代方案或降级"
    
    exit 1
fi

echo ""
echo "🎯 升级完成检查清单："
echo "[ ] 开发服务器启动正常"
echo "[ ] 热更新功能正常"
echo "[ ] 生产构建成功"
echo "[ ] 样式和资源加载正常"
echo "[ ] 核心业务功能正常"
echo "[ ] 构建性能符合预期"
echo ""
echo "📚 相关文档："
echo "- Webpack 5迁移指南: https://webpack.docschina.org/migrate/5/"
echo "- 项目升级记录: scripts/webpack5-upgrade-guide.md"
