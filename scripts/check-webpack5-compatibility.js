#!/usr/bin/env node

/**
 * Webpack 5 兼容性检查脚本
 * 检查当前项目依赖与webpack 5的兼容性
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Webpack 5 兼容版本映射表
const WEBPACK5_COMPATIBLE_VERSIONS = {
  // 核心构建工具
  'webpack': '^5.95.0',
  'webpack-dev-server': '^4.15.2',
  'webpack-merge': '^5.10.0',
  'webpack-bundle-analyzer': '^4.10.2',
  
  // Loaders
  'css-loader': '^6.11.0',
  'less-loader': '^12.2.0',
  'sass-loader': '^16.0.2',
  'style-loader': '^3.3.4',
  'postcss-loader': '^8.1.1',
  'babel-loader': '^9.2.1',
  'file-loader': '^6.2.0', // 注意：webpack 5推荐使用asset modules
  'url-loader': '^4.1.1',  // 注意：webpack 5推荐使用asset modules
  'raw-loader': '^4.0.2',
  'resolve-url-loader': '^5.0.0',
  'style-resources-loader': '^1.5.0',
  
  // Plugins
  'mini-css-extract-plugin': '^2.9.1',
  'html-webpack-plugin': '^5.6.0',
  'terser-webpack-plugin': '^5.3.10',
  'copy-webpack-plugin': '^12.0.2',
  'case-sensitive-paths-webpack-plugin': '^2.4.0',
  'eslint-webpack-plugin': '^4.0.1',
  'script-ext-html-webpack-plugin': '^2.1.5', // 可能需要替换
  'optimize-css-assets-webpack-plugin': null, // 已废弃，使用css-minimizer-webpack-plugin
  
  // React相关
  '@pmmmwh/react-refresh-webpack-plugin': '^0.5.15',
  'react-dev-utils': '^12.0.1',
  'react-refresh': '^0.14.2',
  
  // PostCSS相关
  'autoprefixer': '^10.4.20',
  'postcss-safe-parser': '^7.0.1',
  
  // 新增推荐依赖
  'css-minimizer-webpack-plugin': '^7.0.0', // 替代optimize-css-assets-webpack-plugin
};

// 需要特殊处理的依赖
const SPECIAL_HANDLING = {
  'optimize-css-assets-webpack-plugin': {
    replacement: 'css-minimizer-webpack-plugin',
    reason: 'optimize-css-assets-webpack-plugin已废弃，推荐使用css-minimizer-webpack-plugin'
  },
  'file-loader': {
    replacement: 'webpack asset modules',
    reason: 'webpack 5内置asset modules，推荐替换file-loader'
  },
  'url-loader': {
    replacement: 'webpack asset modules', 
    reason: 'webpack 5内置asset modules，推荐替换url-loader'
  }
};

class Webpack5CompatibilityChecker {
  constructor() {
    this.packageJsonPath = path.join(process.cwd(), 'package.json');
    this.packageJson = this.loadPackageJson();
    this.issues = [];
    this.recommendations = [];
  }

  loadPackageJson() {
    try {
      return JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
    } catch (error) {
      console.error('❌ 无法读取package.json文件');
      process.exit(1);
    }
  }

  checkDependency(depName, currentVersion, depType) {
    const compatibleVersion = WEBPACK5_COMPATIBLE_VERSIONS[depName];
    const specialHandling = SPECIAL_HANDLING[depName];
    
    if (specialHandling) {
      this.issues.push({
        type: 'DEPRECATED',
        package: depName,
        currentVersion,
        issue: specialHandling.reason,
        recommendation: `替换为 ${specialHandling.replacement}`,
        severity: 'HIGH'
      });
      return;
    }
    
    if (!compatibleVersion) {
      // 不在兼容列表中，可能是业务依赖，跳过
      return;
    }
    
    if (compatibleVersion === null) {
      this.issues.push({
        type: 'DEPRECATED',
        package: depName,
        currentVersion,
        issue: '该包已废弃，不兼容webpack 5',
        recommendation: '需要寻找替代方案',
        severity: 'HIGH'
      });
      return;
    }
    
    // 检查版本兼容性
    const needsUpgrade = this.compareVersions(currentVersion, compatibleVersion);
    if (needsUpgrade) {
      this.issues.push({
        type: 'VERSION_INCOMPATIBLE',
        package: depName,
        currentVersion,
        compatibleVersion,
        recommendation: `升级到 ${compatibleVersion}`,
        severity: this.getSeverity(depName)
      });
    }
  }

  compareVersions(current, compatible) {
    // 简化的版本比较，实际项目中建议使用semver库
    const cleanCurrent = current.replace(/[\^~]/, '');
    const cleanCompatible = compatible.replace(/[\^~]/, '');
    
    const currentParts = cleanCurrent.split('.').map(Number);
    const compatibleParts = cleanCompatible.split('.').map(Number);
    
    for (let i = 0; i < Math.max(currentParts.length, compatibleParts.length); i++) {
      const currentPart = currentParts[i] || 0;
      const compatiblePart = compatibleParts[i] || 0;
      
      if (currentPart < compatiblePart) return true;
      if (currentPart > compatiblePart) return false;
    }
    
    return false;
  }

  getSeverity(packageName) {
    const highRiskPackages = [
      'webpack', 'webpack-dev-server', 'mini-css-extract-plugin', 
      'copy-webpack-plugin', 'html-webpack-plugin'
    ];
    
    return highRiskPackages.includes(packageName) ? 'HIGH' : 'MEDIUM';
  }

  checkAllDependencies() {
    console.log('🔍 开始检查webpack 5兼容性...\n');
    
    // 检查devDependencies
    if (this.packageJson.devDependencies) {
      Object.entries(this.packageJson.devDependencies).forEach(([name, version]) => {
        this.checkDependency(name, version, 'devDependencies');
      });
    }
    
    // 检查dependencies中的构建相关依赖
    if (this.packageJson.dependencies) {
      Object.entries(this.packageJson.dependencies).forEach(([name, version]) => {
        if (WEBPACK5_COMPATIBLE_VERSIONS[name] || SPECIAL_HANDLING[name]) {
          this.checkDependency(name, version, 'dependencies');
        }
      });
    }
  }

  generateReport() {
    console.log('📊 兼容性检查报告\n');
    console.log('='.repeat(60));
    
    if (this.issues.length === 0) {
      console.log('✅ 恭喜！所有依赖都与webpack 5兼容');
      return;
    }
    
    // 按严重程度分组
    const highIssues = this.issues.filter(issue => issue.severity === 'HIGH');
    const mediumIssues = this.issues.filter(issue => issue.severity === 'MEDIUM');
    
    if (highIssues.length > 0) {
      console.log('🔴 高风险问题 (必须解决):');
      highIssues.forEach(issue => this.printIssue(issue));
      console.log();
    }
    
    if (mediumIssues.length > 0) {
      console.log('🟡 中等风险问题 (建议解决):');
      mediumIssues.forEach(issue => this.printIssue(issue));
      console.log();
    }
    
    console.log('📋 升级建议:');
    this.generateUpgradeCommands();
  }

  printIssue(issue) {
    console.log(`  📦 ${issue.package}`);
    console.log(`     当前版本: ${issue.currentVersion}`);
    if (issue.compatibleVersion) {
      console.log(`     兼容版本: ${issue.compatibleVersion}`);
    }
    console.log(`     问题: ${issue.issue || '版本不兼容'}`);
    console.log(`     建议: ${issue.recommendation}`);
    console.log();
  }

  generateUpgradeCommands() {
    const upgradeCommands = this.issues
      .filter(issue => issue.type === 'VERSION_INCOMPATIBLE')
      .map(issue => `${issue.package}@${issue.compatibleVersion.replace('^', '')}`)
      .join(' ');
    
    if (upgradeCommands) {
      console.log('💡 批量升级命令:');
      console.log(`npm install --save-dev ${upgradeCommands}`);
      console.log();
    }
    
    // 特殊处理的包
    const specialIssues = this.issues.filter(issue => issue.type === 'DEPRECATED');
    if (specialIssues.length > 0) {
      console.log('⚠️  需要手动处理的包:');
      specialIssues.forEach(issue => {
        console.log(`  - ${issue.package}: ${issue.recommendation}`);
      });
    }
  }

  run() {
    this.checkAllDependencies();
    this.generateReport();
    
    const hasHighRiskIssues = this.issues.some(issue => issue.severity === 'HIGH');
    process.exit(hasHighRiskIssues ? 1 : 0);
  }
}

// 运行检查
const checker = new Webpack5CompatibilityChecker();
checker.run();
