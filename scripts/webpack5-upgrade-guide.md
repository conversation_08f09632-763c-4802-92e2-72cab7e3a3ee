# Webpack 5 渐进式升级指南

## 升级前准备

### 1. 环境检查
```bash
# 检查Node.js版本 (webpack 5需要Node.js 10.13.0+)
node --version

# 检查当前依赖兼容性
node scripts/check-webpack5-compatibility.js

# 创建升级分支
git checkout -b feature/webpack5-upgrade
```

### 2. 备份关键文件
```bash
# 备份package.json
cp package.json package.json.backup

# 备份webpack配置
cp -r config config.backup
```

## 分阶段升级计划

### 阶段1: 核心依赖升级 (风险: 🔴 高)

#### 1.1 升级webpack核心
```bash
npm install --save-dev webpack@^5.95.0
npm install --save-dev webpack-dev-server@^4.15.2
npm install --save-dev webpack-merge@^5.10.0
```

#### 1.2 测试基础构建
```bash
npm run build:test
```

**预期问题及解决方案:**
- `futureEmitAssets` 配置已废弃 → 直接删除
- `webpack-merge` API变更 → 更新导入方式
- `webpack-dev-server` 配置变更 → 更新devServer配置

### 阶段2: CSS相关Loader升级 (风险: 🟡 中)

#### 2.1 升级CSS处理器
```bash
npm install --save-dev css-loader@^6.11.0
npm install --save-dev mini-css-extract-plugin@^2.9.1
npm install --save-dev postcss-loader@^8.1.1
npm install --save-dev less-loader@^12.2.0
npm install --save-dev sass-loader@^16.0.2
```

#### 2.2 更新CSS配置
- 更新 `css-loader` 的 `modules` 配置语法
- 调整 `mini-css-extract-plugin` 配置选项

#### 2.3 测试样式构建
```bash
npm start
# 检查样式是否正常加载
```

### 阶段3: Plugin升级 (风险: 🔴 高)

#### 3.1 升级HTML和压缩插件
```bash
npm install --save-dev html-webpack-plugin@^5.6.0
npm install --save-dev terser-webpack-plugin@^5.3.10
npm install --save-dev copy-webpack-plugin@^12.0.2
```

#### 3.2 替换废弃插件
```bash
# 移除废弃的CSS压缩插件
npm uninstall optimize-css-assets-webpack-plugin

# 安装新的CSS压缩插件
npm install --save-dev css-minimizer-webpack-plugin@^7.0.0
```

#### 3.3 更新插件配置
- 更新 `html-webpack-plugin` 配置
- 替换 `OptimizeCSSAssetsPlugin` 为 `CssMinimizerPlugin`
- 更新 `copy-webpack-plugin` 配置语法

### 阶段4: Asset处理优化 (风险: 🟡 中)

#### 4.1 使用webpack 5内置Asset Modules
```javascript
// 替换 file-loader 和 url-loader
{
  test: /\.(png|jpe?g|gif|svg|woff|woff2|eot|ttf)$/,
  type: 'asset',
  parser: {
    dataUrlCondition: {
      maxSize: 10 * 1024 // 10kb
    }
  },
  generator: {
    filename: 'static/img/[name].[hash:8][ext]'
  }
}
```

#### 4.2 移除不需要的loader
```bash
npm uninstall file-loader url-loader
```

### 阶段5: 开发体验优化 (风险: 🟢 低)

#### 5.1 升级React相关工具
```bash
npm install --save-dev @pmmmwh/react-refresh-webpack-plugin@^0.5.15
npm install --save-dev react-refresh@^0.14.2
```

#### 5.2 更新ESLint集成
```bash
npm install --save-dev eslint-webpack-plugin@^4.0.1
```

## 配置文件更新清单

### webpack.common.js 更新要点
1. ✅ 移除 `futureEmitAssets: true`
2. ✅ 更新 `css-loader` modules配置
3. ✅ 替换 file-loader/url-loader 为 asset modules
4. ✅ 更新 `copy-webpack-plugin` 配置语法

### webpack.prod.js 更新要点
1. ✅ 替换 `OptimizeCSSAssetsPlugin` 为 `CssMinimizerPlugin`
2. ✅ 更新 `terser-webpack-plugin` 配置
3. ✅ 调整 `splitChunks` 配置

### webpack-dev-server 更新要点
1. ✅ 更新 `devServer` 配置选项
2. ✅ 调整热更新配置
3. ✅ 更新代理配置

## 测试验证清单

### 构建测试
- [ ] `npm run build:test` 成功
- [ ] `npm run build:qa` 成功  
- [ ] `npm run build:prod` 成功
- [ ] 构建产物大小对比
- [ ] 构建时间对比

### 开发测试
- [ ] `npm start` 启动成功
- [ ] 热更新功能正常
- [ ] 样式加载正常
- [ ] 图片资源加载正常
- [ ] 代码分割功能正常

### 功能测试
- [ ] 页面正常渲染
- [ ] 路由跳转正常
- [ ] API请求正常
- [ ] 第三方库功能正常

## 性能对比

升级前后需要对比的指标:
- 构建时间
- 包体积大小
- 首屏加载时间
- 热更新速度

## 回滚方案

如果升级过程中遇到无法解决的问题:

```bash
# 恢复package.json
cp package.json.backup package.json

# 恢复webpack配置
rm -rf config
cp -r config.backup config

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 切回原分支
git checkout main
git branch -D feature/webpack5-upgrade
```

## 常见问题解决

### 1. Module not found错误
- 检查 `resolve.fallback` 配置
- 添加Node.js polyfills

### 2. CSS模块化问题
- 更新 `css-loader` modules配置语法
- 检查 `localIdentName` 配置

### 3. 热更新失效
- 检查 `react-refresh-webpack-plugin` 配置
- 更新 `webpack-dev-server` 配置

### 4. 构建性能下降
- 启用持久化缓存
- 优化 `splitChunks` 配置
- 检查loader配置

## 升级完成检查

- [ ] 所有构建命令正常运行
- [ ] 开发环境功能完整
- [ ] 生产环境构建成功
- [ ] 性能指标达到预期
- [ ] 团队成员验证通过
- [ ] 文档更新完成
