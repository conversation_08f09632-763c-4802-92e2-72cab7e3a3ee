#!/usr/bin/env node

/**
 * Webpack 5 升级验证脚本
 * 自动化测试升级后的构建流程和功能
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

class Webpack5UpgradeValidator {
  constructor() {
    this.results = {
      buildTests: [],
      devTests: [],
      performanceTests: [],
      functionalTests: []
    };
    this.startTime = Date.now();
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = {
      info: '📋',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      performance: '⚡'
    }[type] || '📋';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  async runCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      try {
        const result = execSync(command, {
          encoding: 'utf8',
          timeout: options.timeout || 300000, // 5分钟超时
          ...options
        });
        
        const duration = Date.now() - startTime;
        resolve({ success: true, output: result, duration });
      } catch (error) {
        const duration = Date.now() - startTime;
        resolve({ 
          success: false, 
          error: error.message, 
          output: error.stdout || error.stderr,
          duration 
        });
      }
    });
  }

  async testBuildCommands() {
    this.log('开始测试构建命令...', 'info');
    
    const buildCommands = [
      { name: 'build:test', command: 'npm run build:test' },
      { name: 'build:qa', command: 'npm run build:qa' },
      { name: 'build:prod', command: 'npm run build:prod' },
      { name: 'skeleton', command: 'npm run skeleton' }
    ];

    for (const { name, command } of buildCommands) {
      this.log(`测试 ${name}...`);
      
      const result = await this.runCommand(command, { timeout: 600000 }); // 10分钟
      
      this.results.buildTests.push({
        name,
        success: result.success,
        duration: result.duration,
        error: result.error
      });

      if (result.success) {
        this.log(`${name} 构建成功 (${(result.duration / 1000).toFixed(2)}s)`, 'success');
      } else {
        this.log(`${name} 构建失败: ${result.error}`, 'error');
      }
    }
  }

  async testDevServer() {
    this.log('测试开发服务器...', 'info');
    
    return new Promise((resolve) => {
      const devServer = spawn('npm', ['start'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, NODE_ENV: 'development' }
      });

      let output = '';
      let serverStarted = false;
      const timeout = setTimeout(() => {
        if (!serverStarted) {
          devServer.kill();
          this.results.devTests.push({
            name: 'dev-server-start',
            success: false,
            error: '开发服务器启动超时'
          });
          this.log('开发服务器启动超时', 'error');
          resolve();
        }
      }, 120000); // 2分钟超时

      devServer.stdout.on('data', (data) => {
        output += data.toString();
        
        // 检查服务器是否启动成功
        if (output.includes('webpack compiled') || output.includes('Compiled successfully')) {
          serverStarted = true;
          clearTimeout(timeout);
          
          this.results.devTests.push({
            name: 'dev-server-start',
            success: true,
            duration: Date.now() - this.startTime
          });
          
          this.log('开发服务器启动成功', 'success');
          
          // 测试热更新
          setTimeout(() => {
            this.testHotReload(devServer, resolve);
          }, 5000);
        }
      });

      devServer.stderr.on('data', (data) => {
        const error = data.toString();
        if (error.includes('Error') || error.includes('Failed')) {
          clearTimeout(timeout);
          devServer.kill();
          
          this.results.devTests.push({
            name: 'dev-server-start',
            success: false,
            error: error
          });
          
          this.log(`开发服务器启动失败: ${error}`, 'error');
          resolve();
        }
      });
    });
  }

  testHotReload(devServer, resolve) {
    this.log('测试热更新功能...', 'info');
    
    // 创建一个临时文件来测试热更新
    const testFile = path.join(process.cwd(), 'src/test-hot-reload.js');
    const testContent = `// Hot reload test - ${Date.now()}
export const testValue = ${Math.random()};`;
    
    try {
      fs.writeFileSync(testFile, testContent);
      
      // 监听输出变化
      let hotReloadDetected = false;
      const hotReloadTimeout = setTimeout(() => {
        if (!hotReloadDetected) {
          this.results.devTests.push({
            name: 'hot-reload',
            success: false,
            error: '热更新功能测试超时'
          });
          this.log('热更新功能测试超时', 'warning');
        }
        
        // 清理测试文件
        try {
          fs.unlinkSync(testFile);
        } catch (e) {}
        
        devServer.kill();
        resolve();
      }, 30000); // 30秒超时

      devServer.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('webpack compiled') && !hotReloadDetected) {
          hotReloadDetected = true;
          clearTimeout(hotReloadTimeout);
          
          this.results.devTests.push({
            name: 'hot-reload',
            success: true
          });
          
          this.log('热更新功能正常', 'success');
          
          // 清理测试文件
          try {
            fs.unlinkSync(testFile);
          } catch (e) {}
          
          devServer.kill();
          resolve();
        }
      });
      
    } catch (error) {
      this.results.devTests.push({
        name: 'hot-reload',
        success: false,
        error: error.message
      });
      
      this.log(`热更新测试失败: ${error.message}`, 'error');
      devServer.kill();
      resolve();
    }
  }

  async testBuildPerformance() {
    this.log('测试构建性能...', 'performance');
    
    // 清理之前的构建产物
    await this.runCommand('rm -rf dist');
    
    const performanceTest = async (buildCommand, name) => {
      const startTime = Date.now();
      const result = await this.runCommand(buildCommand);
      const buildTime = Date.now() - startTime;
      
      if (result.success) {
        // 分析构建产物大小
        const distStats = await this.analyzeBuildOutput();
        
        this.results.performanceTests.push({
          name,
          buildTime,
          ...distStats,
          success: true
        });
        
        this.log(`${name} 构建时间: ${(buildTime / 1000).toFixed(2)}s`, 'performance');
        this.log(`${name} 总大小: ${(distStats.totalSize / 1024 / 1024).toFixed(2)}MB`, 'performance');
      } else {
        this.results.performanceTests.push({
          name,
          success: false,
          error: result.error
        });
      }
    };

    await performanceTest('npm run build:prod', 'production-build');
  }

  async analyzeBuildOutput() {
    const distPath = path.join(process.cwd(), 'dist');
    
    if (!fs.existsSync(distPath)) {
      return { totalSize: 0, fileCount: 0 };
    }

    let totalSize = 0;
    let fileCount = 0;
    const fileTypes = {};

    const analyzeDirectory = (dirPath) => {
      const files = fs.readdirSync(dirPath);
      
      files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          analyzeDirectory(filePath);
        } else {
          totalSize += stats.size;
          fileCount++;
          
          const ext = path.extname(file);
          fileTypes[ext] = (fileTypes[ext] || 0) + stats.size;
        }
      });
    };

    analyzeDirectory(distPath);

    return {
      totalSize,
      fileCount,
      fileTypes
    };
  }

  async testFunctionalRequirements() {
    this.log('测试功能需求...', 'info');
    
    const functionalTests = [
      {
        name: 'package-json-integrity',
        test: () => {
          const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
          return packageJson.dependencies && packageJson.devDependencies;
        }
      },
      {
        name: 'webpack-config-syntax',
        test: () => {
          try {
            require('./config/webpack.common.js');
            require('./config/webpack.prod.js');
            return true;
          } catch (error) {
            throw new Error(`Webpack配置语法错误: ${error.message}`);
          }
        }
      },
      {
        name: 'build-output-structure',
        test: () => {
          const distPath = path.join(process.cwd(), 'dist');
          if (!fs.existsSync(distPath)) {
            throw new Error('构建输出目录不存在');
          }
          
          const requiredFiles = ['index.html', 'knowledge.html'];
          const requiredDirs = ['static'];
          
          for (const file of requiredFiles) {
            if (!fs.existsSync(path.join(distPath, file))) {
              throw new Error(`缺少必需文件: ${file}`);
            }
          }
          
          for (const dir of requiredDirs) {
            if (!fs.existsSync(path.join(distPath, dir))) {
              throw new Error(`缺少必需目录: ${dir}`);
            }
          }
          
          return true;
        }
      }
    ];

    for (const { name, test } of functionalTests) {
      try {
        const result = await test();
        this.results.functionalTests.push({
          name,
          success: !!result
        });
        this.log(`功能测试 ${name} 通过`, 'success');
      } catch (error) {
        this.results.functionalTests.push({
          name,
          success: false,
          error: error.message
        });
        this.log(`功能测试 ${name} 失败: ${error.message}`, 'error');
      }
    }
  }

  generateReport() {
    this.log('\n📊 升级验证报告', 'info');
    console.log('='.repeat(60));
    
    const totalTests = Object.values(this.results).flat().length;
    const passedTests = Object.values(this.results).flat().filter(test => test.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`\n总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    // 详细报告
    Object.entries(this.results).forEach(([category, tests]) => {
      if (tests.length > 0) {
        console.log(`\n${category.toUpperCase()}:`);
        tests.forEach(test => {
          const status = test.success ? '✅' : '❌';
          const duration = test.duration ? ` (${(test.duration / 1000).toFixed(2)}s)` : '';
          console.log(`  ${status} ${test.name}${duration}`);
          
          if (!test.success && test.error) {
            console.log(`     错误: ${test.error}`);
          }
        });
      }
    });
    
    // 性能报告
    const perfTests = this.results.performanceTests.filter(test => test.success);
    if (perfTests.length > 0) {
      console.log('\n⚡ 性能指标:');
      perfTests.forEach(test => {
        console.log(`  ${test.name}:`);
        console.log(`    构建时间: ${(test.buildTime / 1000).toFixed(2)}s`);
        console.log(`    输出大小: ${(test.totalSize / 1024 / 1024).toFixed(2)}MB`);
        console.log(`    文件数量: ${test.fileCount}`);
      });
    }
    
    const totalTime = (Date.now() - this.startTime) / 1000;
    console.log(`\n总验证时间: ${totalTime.toFixed(2)}s`);
    
    return failedTests === 0;
  }

  async run() {
    this.log('🚀 开始Webpack 5升级验证...', 'info');
    
    try {
      await this.testBuildCommands();
      await this.testDevServer();
      await this.testBuildPerformance();
      await this.testFunctionalRequirements();
      
      const success = this.generateReport();
      process.exit(success ? 0 : 1);
      
    } catch (error) {
      this.log(`验证过程中发生错误: ${error.message}`, 'error');
      process.exit(1);
    }
  }
}

// 运行验证
const validator = new Webpack5UpgradeValidator();
validator.run();
